/*
 * @Description: 数据资源库路由
 */
export default {
  path: '/data-resource',
  redirect: '/data-resource/home',
  name: 'DataResource',
  component: () => import('@/views/data-resource/resource-layout.vue'),
  children: [
    {
      path: 'home',
      name: 'ResourceHome',
      component: () => import('@/views/data-resource/home-page/index.vue'),
    },
    {
      path: 'browse',
      component: () => import('@/views/data-resource/data-browse/index.vue'),
      name: 'DataBrowse',
    },
    {
      path: 'browse-detail/:id',
      component: () => import('@/views/data-resource/data-browse/data-detail.vue'),
      name: 'DataBrowseDetail',
      props: true,
    },
    {
      path: 'browse-ds/:id',
      component: () => import('@/views/data-resource/data-browse/field-list.vue'),
      name: 'DataSetField',
      props: true,
    },
    {
      path: 'browse-ds-field',
      props: (route) => ({ fieldId: route.query.fieldId }),
      name: 'DataSetFieldDetail',
      component: () => import('@/views/data-resource/data-browse/field-detail.vue'),
    },
    {
      path: 'dataset-detail/:id',
      component: () => import('@/views/data-resource/data-browse/dataset-layout.vue'),
      name: 'DataSetDetail',
      props: true,
    },
    {
      path: 'browse-layout',
      component: () => import('@/views/data-resource/data-browse/data-layout.vue'),
      name: 'DataBrowseLayout',
      props: (route) => ({ orderId: route.query.orderId, code: route.query.code }),
      // children: [
      //   {
      //     path: 'rank',
      //     component: () => import('@/views/data-resource/data-browse/rank.vue'),
      //   },
      //   {
      //     path: 'menu-one',
      //     name: 'DataBrowseMenuOne',
      //     component: () => import('@/views/data-resource/data-browse/menu-one.vue'),
      //   },
      //   {
      //     path: 'data-type/:id',
      //     props: true,
      //     name: 'DataBrowDataType',
      //     component: () => import('@/views/data-resource/data-browse/data-type.vue'),
      //   },
      //   {
      //     path: 'data-field/:id',
      //     props: true,
      //     name: 'DataBrowseDataField',
      //     component: () => import('@/views/data-resource/data-browse/data-field.vue'),
      //   },
      // ],
    },
    {
      path: 'search',
      name: 'ResourceDataSearch',
      component: () => import('@/views/data-resource/data-search/data-search.vue'),
    },
    {
      path: 'tools',
      name: 'ResourceTools',
      component: () => import('@/views/data-resource/resource-tools/index.vue'),
    },
    {
      path: 'user-notice',
      name: 'ResourceUserNotice',
      component: () => import('@/views/data-resource/user-notice/index.vue'),
    },
  ],
};
