<template>
  <div v-loading="loading" class="px-10 pt-5">
    <Breadcrumb :list="breadList" />

    <h2 class="mt-[25px] text-[28px] font-bold">{{ fieldDetail.name }}</h2>
    <p class="border-border mt-5 border-b pb-[26px]">
      {{ fieldDetail.chineseMeaning }}
    </p>

    <!-- 字段基本信息 -->
    <div class="mt-6">
      <h3 class="mb-4 text-xl font-bold">字段信息</h3>
      <el-descriptions direction="horizontal" :column="2" border>
        <el-descriptions-item label="字段ID">{{ fieldDetail.id }}</el-descriptions-item>
        <el-descriptions-item label="变量名称">{{ fieldDetail.name }}</el-descriptions-item>
        <el-descriptions-item label="变量中文含义">{{ fieldDetail.chineseMeaning }}</el-descriptions-item>
        <el-descriptions-item label="数据类型">{{ fieldTypeText(fieldDetail.valueType) }}</el-descriptions-item>
        <el-descriptions-item label="所属表">
          <el-button link type="primary" @click="gotoTable">
            {{ fieldDetail.tableChineseName }}
          </el-button>
        </el-descriptions-item>
        <el-descriptions-item label="表英文名称">{{ fieldDetail.tableName }}</el-descriptions-item>
        <el-descriptions-item label="访问阶段">{{
          fieldDetail.visitPhaseChinese || fieldDetail.visitPhase
        }}</el-descriptions-item>
        <el-descriptions-item label="是否按性别区分">{{ fieldDetail.gendered ? '是' : '否' }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 字段详细描述 -->
    <div v-if="fieldDetail.explanation || fieldDetail.valueExplanation || fieldDetail.notes" class="mt-6">
      <h3 class="mb-4 text-xl font-bold">详细描述</h3>
      <el-descriptions direction="vertical" :column="1" border>
        <el-descriptions-item v-if="fieldDetail.explanation" label="变量说明">
          {{ fieldDetail.explanation }}
        </el-descriptions-item>
        <el-descriptions-item v-if="fieldDetail.valueExplanation" label="变量值说明">
          {{ fieldDetail.valueExplanation }}
        </el-descriptions-item>
        <el-descriptions-item v-if="fieldDetail.notes" label="变量备注">
          {{ fieldDetail.notes }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 统计信息 -->
    <div v-if="fieldDetail.instanceFieldStatistics && fieldDetail.instanceFieldStatistics.length > 0" class="mt-6">
      <h3 class="mb-4 text-xl font-bold">统计信息</h3>
      <div v-for="(stat, index) in fieldDetail.instanceFieldStatistics" :key="index" class="mb-4">
        <el-descriptions direction="horizontal" :column="4" border>
          <el-descriptions-item label="参与者数量">{{ stat.participantCount }}</el-descriptions-item>
          <el-descriptions-item label="记录数">{{ stat.recordCount }}</el-descriptions-item>
          <el-descriptions-item v-if="stat.maxLength" label="最大长度">{{ stat.maxLength }}</el-descriptions-item>
          <el-descriptions-item v-if="stat.minLength" label="最小长度">{{ stat.minLength }}</el-descriptions-item>
          <el-descriptions-item v-if="stat.averageLength" label="平均长度">{{
            stat.averageLength
          }}</el-descriptions-item>
          <el-descriptions-item v-if="stat.instanceDescription" label="实例说明" :span="2">{{
            stat.instanceDescription
          }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <!-- 数据分布图表 -->
    <div v-if="showChart" class="mt-6">
      <h3 class="mb-4 text-xl font-bold">数据分布</h3>
      <div class="border-border border px-10 py-6">
        <div class="h-[300px]">
          <Chart v-if="fieldDetail.chineseMeaning === '性别'" :option="pieOption" />
          <Chart v-else :option="barOption" />
        </div>
        <p class="text-tip mt-4 text-sm">
          数据统计最后更新于{{ fieldDetail.rltTime?.updateTime || fieldDetail.rltTime?.createTime }}
        </p>
      </div>
    </div>

    <!-- 返回按钮 -->
    <div class="mt-8 mb-6">
      <el-button @click="gotoTable">返回数据表</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { getMedicalFieldDetail, findAlternativeByMedicalFieldId } from '@/api';
  import Breadcrumb from './components/Breadcrumb.vue';
  import Chart from '@/components/Chart.vue';
  import { fieldTypeText } from '@/utils/format';
  import { useRouter } from 'vue-router';

  // 扩展的统计信息接口
  interface ExtendedStatisticVO extends StatisticVO {
    instanceId?: number;
    instanceSerialNumber?: string;
    instanceDescription?: string;
    maxLength?: number;
    minLength?: number;
    averageLength?: number;
  }

  interface Props {
    id: string;
    tableId: string;
    fieldId: string;
  }
  const props = defineProps<Props>();
  const emit = defineEmits(['goto-table']);
  const router = useRouter();

  const loading = ref(false);
  const fieldDetail = ref<MedicalFieldDetailVO>({} as any);
  const showChart = ref(false);

  const breadList = ref([
    { name: '数据资源', id: '', type: 'home' },
    { name: '数据浏览', id: '', type: 'browse' },
    { name: '数据集详情', id: props.id, type: 'dataset' },
    { name: '数据表详情', id: props.tableId, type: 'table' },
    { name: '字段详情', id: props.fieldId, type: 'field' },
  ]);

  const barOption = reactive({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
    },
    grid: {
      left: 0,
      right: 0,
      bottom: 0,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: [],
      boundaryGap: true,
      axisTick: { show: false },
      axisLabel: { color: '#565B5C' },
      axisLine: { lineStyle: { color: '#C8C9CC' } },
    },
    yAxis: {
      type: 'value',
      axisLabel: { color: '#939899' },
    },
    color: ['#1296B3'],
    series: {
      type: 'bar',
      barMaxWidth: '50px',
      data: [],
      label: { show: true, position: 'top' },
    },
  });

  const pieOption = reactive({
    tooltip: { trigger: 'item' },
    series: {
      type: 'pie',
      radius: '70%',
      data: [],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
      },
      label: {
        color: '#939899',
        formatter: '{b} \n\r{@[]}({d}%)',
      },
    },
  });

  const gotoTable = () => {
    router.push({
      name: 'DataSetDetail',
      params: { id: props.id },
      query: { tableId: props.tableId },
    });
  };

  async function fetchFieldDetail() {
    try {
      loading.value = true;
      const { data } = await getMedicalFieldDetail(+props.fieldId);
      fieldDetail.value = data || ({} as any);
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  async function fetchChartData() {
    try {
      const { data } = await findAlternativeByMedicalFieldId(+props.fieldId);
      if (data && data.length > 0) {
        showChart.value = true;

        let xData = [];
        let sData = [];
        let pieData = [];

        data.forEach((item) => {
          xData.push(item.binMidpoint);
          sData.push({
            value: item.frequency || 0,
            percentileFlag: item.percentileFlag,
          });
          pieData.push({
            name: item.binMidpoint,
            value: item.frequency || 0,
          });
        });

        barOption.xAxis.data = xData;
        barOption.series.data = sData;
        pieOption.series.data = pieData;
      }
    } catch (error) {
      console.log(error);
    }
  }

  watch(
    () => props.fieldId,
    () => {
      if (props.fieldId) {
        fetchFieldDetail();
        fetchChartData();
      }
    },
    { immediate: true }
  );
</script>

<style lang="scss" scoped>
  :deep(.el-descriptions) {
    .el-descriptions__body {
      background-color: transparent;
    }
    .el-descriptions__label {
      color: $color-tip-text;
      font-weight: 500;
    }
  }
</style>
