<template>
  <div class="flex h-full flex-col bg-baf">
    <div class="flex h-0 flex-1">
      <DatasetAsideBar :dataset-id="props.datasetId" />

      <el-scrollbar height="100%" class="w-0 flex-1 bg-w text-sm">
        <DatasetOverview v-if="componentId === 'overview'" :id="datasetId" />
        <DatasetTable v-if="componentId === 'table'" :id="datasetId" :table-id="tableId" @goto-field="gotoField" />
        <DatasetField v-if="componentId === 'field'" :id="datasetId" :table-id="tableId" :field-id="fieldId" @goto-table="gotoTable" />
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
  import DatasetOverview from './dataset-overview.vue';
  import DatasetTable from './dataset-table.vue';
  import DatasetField from './dataset-field.vue';
  import DatasetAsideBar from './components/DatasetAsideBar.vue';
  import { useRouter } from 'vue-router';

  interface Props {
    datasetId: string;
  }
  const props = defineProps<Props>();
  const router = useRouter();

  const componentId = ref('overview');
  const tableId = ref('');
  const fieldId = ref('');

  const gotoTable = (id: string) => {
    tableId.value = id;
    componentId.value = 'table';
  };

  const gotoField = (tId: string, fId: string) => {
    tableId.value = tId;
    fieldId.value = fId;
    componentId.value = 'field';
  };

  // 监听路由参数变化
  watch(
    () => router.currentRoute.value.query,
    (query) => {
      if (query.tableId) {
        tableId.value = query.tableId as string;
        componentId.value = 'table';
      }
      if (query.fieldId) {
        fieldId.value = query.fieldId as string;
        componentId.value = 'field';
      }
      if (!query.tableId && !query.fieldId) {
        componentId.value = 'overview';
      }
    },
    { immediate: true }
  );
</script>
