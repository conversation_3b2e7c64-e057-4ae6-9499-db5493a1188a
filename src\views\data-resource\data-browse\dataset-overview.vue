<template>
  <div v-loading="loading" class="px-10 pt-5">
    <Breadcrumb :list="breadList" />

    <h2 class="mt-[25px] text-[28px] font-bold">{{ detailData.datasetNameCn }}</h2>
    <p class="mt-5 border-b border-border pb-[26px]">
      {{ detailData.description }}
    </p>

    <!-- 基本信息 -->
    <div class="mt-6">
      <h3 class="text-xl font-bold mb-4">基本信息</h3>
      <el-descriptions direction="horizontal" :column="2" border>
        <el-descriptions-item label="数据集名称(中文)">{{ detailData.datasetNameCn }}</el-descriptions-item>
        <el-descriptions-item label="数据集名称(英文)">{{ detailData.datasetName }}</el-descriptions-item>
        <el-descriptions-item label="课题编码缩写">{{ detailData.projectCode }}</el-descriptions-item>
        <el-descriptions-item label="疾病类型">{{ detailData.diseaseTypeAnnotation }}</el-descriptions-item>
        <el-descriptions-item label="更新日期">{{ detailData.createDate }}</el-descriptions-item>
        <el-descriptions-item label="数据负责人">{{ detailData.dataManager }}</el-descriptions-item>
        <el-descriptions-item label="所属单位">{{ detailData.affiliatedUnit }}</el-descriptions-item>
        <el-descriptions-item label="所属项目">{{ detailData.projectName }}</el-descriptions-item>
        <el-descriptions-item label="项目负责人">{{ detailData.projectLeader }}</el-descriptions-item>
        <el-descriptions-item label="上传人">{{ detailData.uploader }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ detailData.contactPhone }}</el-descriptions-item>
        <el-descriptions-item label="办公邮箱">{{ detailData.officeEmail }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 数据特征 -->
    <div class="mt-8">
      <h3 class="text-xl font-bold mb-4">数据特征</h3>
      <el-descriptions direction="horizontal" :column="2" border>
        <el-descriptions-item label="是否有生物样本">{{ detailData.hasBiologicalSample }}</el-descriptions-item>
        <el-descriptions-item label="是否有影像数据">{{ detailData.hasImagingData }}</el-descriptions-item>
        <el-descriptions-item label="是否有脑电数据">{{ detailData.hasEEGData }}</el-descriptions-item>
        <el-descriptions-item label="生物样本类型">{{ detailData.biologicalSampleType }}</el-descriptions-item>
        <el-descriptions-item label="数据模态类型">{{ detailData.dataModalityType }}</el-descriptions-item>
        <el-descriptions-item label="数据信号类型">{{ detailData.dataSignalType }}</el-descriptions-item>
        <el-descriptions-item label="其他数据类型">{{ detailData.otherDataTypes }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 数据表统计 -->
    <template v-if="tables.length > 0">
      <h3 class="mt-8 text-xl font-bold">数据表 ({{ tables.length }})</h3>
      <el-table :data="tables" style="width: 100%" class="c-table-header mb-4 mt-4">
        <el-table-column prop="id" label="表ID" width="100">
          <template #default="{ row }">
            <el-button link type="primary" @click="gotoTable(row)">
              {{ row.id }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="tableChineseName" label="表中文名称" min-width="150px">
          <template #default="{ row }">
            {{ row.tableChineseName }}<span v-if="row.visitPhaseChinese">（{{ row.visitPhaseChinese }}）</span>
          </template>
        </el-table-column>
        <el-table-column prop="tableName" label="表英文名称" min-width="150px">
          <template #default="{ row }">
            {{ row.tableName }}<span v-if="row.visitPhase">（{{ row.visitPhase }}）</span>
          </template>
        </el-table-column>
        <el-table-column prop="fieldCount" label="字段数量" width="100">
          <template #default="{ row }">
            {{ row.fieldCount || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="recordCount" label="记录数量" width="100">
          <template #default="{ row }">
            {{ row.recordCount || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button link type="primary" @click="gotoTable(row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </template>
  </div>
</template>

<script setup lang="ts">
  import { findEntityById_36, findTablesByFileInforId } from '@/api';
  import Breadcrumb from './components/Breadcrumb.vue';
  import { useRouter } from 'vue-router';

  interface Props {
    id: string;
  }
  const props = defineProps<Props>();
  const emit = defineEmits(['goto-table']);
  const router = useRouter();

  const loading = ref(false);
  const detailData = ref<FileInfoVO>({} as any);
  const tables = ref<CBDDefTableVO[]>([]);

  const breadList = ref([
    { name: '数据资源', id: '', type: 'home' },
    { name: '数据浏览', id: '', type: 'browse' },
    { name: '数据集详情', id: props.id, type: 'dataset' },
  ]);

  const gotoTable = (table: CBDDefTableVO) => {
    router.push({ 
      name: 'DataSetDetail', 
      params: { id: props.id },
      query: { tableId: table.id }
    });
  };

  async function fetchDetail() {
    try {
      loading.value = true;
      const { data } = await findEntityById_36(+props.id);
      detailData.value = data || ({} as any);
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  async function fetchTables() {
    try {
      const { data } = await findTablesByFileInforId(+props.id, 1, 999);
      tables.value = data?.content || [];
    } catch (error) {
      console.log(error);
    }
  }

  onMounted(() => {
    fetchDetail();
    fetchTables();
  });
</script>

<style lang="scss" scoped>
  :deep(.el-descriptions) {
    .el-descriptions__body {
      background-color: transparent;
    }
    .el-descriptions__label {
      color: $color-tip-text;
      font-weight: 500;
    }
  }
</style>
