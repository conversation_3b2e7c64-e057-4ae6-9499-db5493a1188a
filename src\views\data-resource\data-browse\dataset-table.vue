<template>
  <div v-loading="loading" class="px-10 pt-5">
    <Breadcrumb :list="breadList" />

    <h2 class="mt-[25px] text-[28px] font-bold">{{ tableInfo.tableChineseName }}</h2>
    <p class="mt-5 border-b border-border pb-[26px]">
      {{ tableInfo.tableName }}
      <span v-if="tableInfo.visitPhaseChinese">（{{ tableInfo.visitPhaseChinese }}）</span>
    </p>

    <!-- 表基本信息 -->
    <div class="mt-6">
      <h3 class="text-xl font-bold mb-4">表信息</h3>
      <el-descriptions direction="horizontal" :column="2" border>
        <el-descriptions-item label="表ID">{{ tableInfo.id }}</el-descriptions-item>
        <el-descriptions-item label="表中文名称">{{ tableInfo.tableChineseName }}</el-descriptions-item>
        <el-descriptions-item label="表英文名称">{{ tableInfo.tableName }}</el-descriptions-item>
        <el-descriptions-item label="访问阶段">{{ tableInfo.visitPhaseChinese || tableInfo.visitPhase }}</el-descriptions-item>
        <el-descriptions-item label="字段数量">{{ fieldTotal }}</el-descriptions-item>
        <el-descriptions-item label="记录数量">{{ tableInfo.recordCount || '-' }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 数据字段列表 -->
    <template v-if="fieldTotal > 0">
      <div class="mt-8 flex items-center justify-between">
        <h3 class="text-xl font-bold">数据字段 ({{ fieldTotal }})</h3>
        <div class="flex items-center gap-4">
          <el-input
            v-model="searchVal"
            placeholder="搜索字段"
            style="width: 300px"
            clearable
            @clear="fetchFields()"
            @keyup.enter="fetchFields()"
          >
            <template #append>
              <el-button :icon="Search" @click="fetchFields()" />
            </template>
          </el-input>
        </div>
      </div>
      
      <el-table
        :key="'fieldTable' + tableId"
        :data="fieldData"
        style="width: 100%"
        class="c-table-header mb-4 mt-4"
      >
        <el-table-column prop="id" label="字段ID" width="100">
          <template #default="{ row }">
            <el-button link type="primary" @click="gotoField(row)">
              {{ row.id }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="变量名称" min-width="120" />
        <el-table-column prop="chineseMeaning" label="变量中文含义" min-width="150" />
        <el-table-column label="类型" width="100">
          <template #default="{ row }">
            <span>{{ fieldTypeText(row.valueType) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="explanation" label="变量说明" min-width="200" show-overflow-tooltip />
        <el-table-column prop="valueExplanation" label="变量值说明" min-width="200" show-overflow-tooltip />
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button link type="primary" @click="gotoField(row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="mt-5 flex justify-center">
        <el-pagination
          background
          layout="total, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
  import { findMedicalFieldsByTableId, findTablesByFileInforId } from '@/api';
  import Breadcrumb from './components/Breadcrumb.vue';
  import { fieldTypeText } from '@/utils/format';
  import { Search } from '@element-plus/icons-vue';
  import { useRouter } from 'vue-router';

  interface Props {
    id: string;
    tableId: string;
  }
  const props = defineProps<Props>();
  const emit = defineEmits(['goto-field']);
  const router = useRouter();

  const loading = ref(false);
  const tableInfo = ref<CBDDefTableVO>({} as any);
  const fieldData = ref<MedicalFieldVO[]>([]);
  const searchVal = ref('');
  const total = ref(0);
  const pagination = reactive({
    page: 1,
    pageSize: 15,
  });

  const fieldTotal = computed(() => {
    return total.value;
  });

  const breadList = ref([
    { name: '数据资源', id: '', type: 'home' },
    { name: '数据浏览', id: '', type: 'browse' },
    { name: '数据集详情', id: props.id, type: 'dataset' },
    { name: '数据表详情', id: props.tableId, type: 'table' },
  ]);

  const gotoField = (field: MedicalFieldVO) => {
    router.push({ 
      name: 'DataSetDetail', 
      params: { id: props.id },
      query: { tableId: props.tableId, fieldId: field.id }
    });
  };

  const handleCurrentChange = (page: number) => {
    pagination.page = page;
    fetchFields();
  };

  async function fetchTableInfo() {
    try {
      const { data } = await findTablesByFileInforId(+props.id, 1, 999);
      const tables = data?.content || [];
      tableInfo.value = tables.find(t => t.id === +props.tableId) || ({} as any);
    } catch (error) {
      console.log(error);
    }
  }

  async function fetchFields() {
    try {
      loading.value = true;
      const { data } = await findMedicalFieldsByTableId({
        cbdTableId: +props.tableId,
        pageNum: pagination.page,
        pageSize: pagination.pageSize,
        searchInput: searchVal.value,
      });
      fieldData.value = data?.content || [];
      total.value = data?.totalElement || 0;
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  watch(
    () => props.tableId,
    () => {
      if (props.tableId) {
        fetchTableInfo();
        fetchFields();
      }
    },
    { immediate: true }
  );
</script>

<style lang="scss" scoped>
  :deep(.el-descriptions) {
    .el-descriptions__body {
      background-color: transparent;
    }
    .el-descriptions__label {
      color: $color-tip-text;
      font-weight: 500;
    }
  }
</style>
