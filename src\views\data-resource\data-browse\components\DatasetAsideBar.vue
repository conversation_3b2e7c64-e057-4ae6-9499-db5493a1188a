<template>
  <div class="aside bg-w flex flex-col">
    <div class="border-b border-[#e8eaed] py-4 pl-5">
      <h3 class="text-lg font-bold">数据集结构</h3>
    </div>
    
    <el-scrollbar class="h-0 flex-1">
      <div v-loading="loading" class="p-4">
        <!-- 数据集概览 -->
        <div class="mb-4">
          <div 
            class="flex cursor-pointer items-center py-2 px-3 rounded hover:bg-gray-50"
            :class="{ 'bg-blue-50 text-blue-600': currentView === 'overview' }"
            @click="gotoOverview"
          >
            <el-icon class="mr-2"><Document /></el-icon>
            <span class="font-medium">数据集概览</span>
          </div>
        </div>

        <!-- 数据表列表 -->
        <div v-if="tables.length > 0">
          <h4 class="mb-2 text-sm font-medium text-gray-600">数据表 ({{ tables.length }})</h4>
          <div v-for="table in tables" :key="table.id" class="mb-2">
            <div 
              class="flex cursor-pointer items-center justify-between py-2 px-3 rounded hover:bg-gray-50"
              :class="{ 'bg-blue-50 text-blue-600': currentView === 'table' && currentTableId === table.id }"
              @click="gotoTable(table)"
            >
              <div class="flex items-center flex-1 min-w-0">
                <el-icon class="mr-2 flex-shrink-0"><Grid /></el-icon>
                <div class="min-w-0 flex-1">
                  <div class="text-sm font-medium truncate">{{ table.tableChineseName }}</div>
                  <div class="text-xs text-gray-500 truncate">{{ table.tableName }}</div>
                </div>
              </div>
              <el-icon 
                class="ml-2 flex-shrink-0 transition-transform"
                :class="{ 'rotate-90': expandedTables.has(table.id!) }"
              >
                <ArrowRight />
              </el-icon>
            </div>
            
            <!-- 字段列表 -->
            <div v-if="expandedTables.has(table.id!)" class="ml-6 mt-1">
              <div v-loading="fieldLoading[table.id!]" class="space-y-1">
                <div 
                  v-for="field in tableFields[table.id!] || []" 
                  :key="field.id"
                  class="flex cursor-pointer items-center py-1 px-2 rounded text-sm hover:bg-gray-50"
                  :class="{ 'bg-blue-50 text-blue-600': currentView === 'field' && currentFieldId === field.id }"
                  @click="gotoField(table, field)"
                >
                  <el-icon class="mr-2 text-xs"><Document /></el-icon>
                  <div class="min-w-0 flex-1">
                    <div class="truncate">{{ field.chineseMeaning || field.name }}</div>
                    <div class="text-xs text-gray-400 truncate">{{ field.name }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
  import { findTablesByFileInforId, findMedicalFieldsByTableId } from '@/api';
  import { Document, Grid, ArrowRight } from '@element-plus/icons-vue';
  import { useRouter } from 'vue-router';

  interface Props {
    datasetId: string;
  }
  const props = defineProps<Props>();
  const router = useRouter();

  const loading = ref(false);
  const tables = ref<CBDDefTableVO[]>([]);
  const expandedTables = ref(new Set<number>());
  const tableFields = ref<Record<number, MedicalFieldVO[]>>({});
  const fieldLoading = ref<Record<number, boolean>>({});

  const currentView = ref('overview');
  const currentTableId = ref<number>();
  const currentFieldId = ref<number>();

  // 获取数据表列表
  async function fetchTables() {
    try {
      loading.value = true;
      const { data } = await findTablesByFileInforId(+props.datasetId, 1, 999);
      tables.value = data?.content || [];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  // 获取表字段
  async function fetchTableFields(tableId: number) {
    if (tableFields.value[tableId]) return;
    
    try {
      fieldLoading.value[tableId] = true;
      const { data } = await findMedicalFieldsByTableId({
        cbdTableId: tableId,
        pageNum: 1,
        pageSize: 999,
      });
      tableFields.value[tableId] = data?.content || [];
    } catch (error) {
      console.log(error);
    } finally {
      fieldLoading.value[tableId] = false;
    }
  }

  const gotoOverview = () => {
    currentView.value = 'overview';
    currentTableId.value = undefined;
    currentFieldId.value = undefined;
    router.push({ 
      name: 'DataSetDetail', 
      params: { id: props.datasetId },
      query: {}
    });
  };

  const gotoTable = async (table: CBDDefTableVO) => {
    currentView.value = 'table';
    currentTableId.value = table.id;
    currentFieldId.value = undefined;
    
    // 切换展开状态
    if (expandedTables.value.has(table.id!)) {
      expandedTables.value.delete(table.id!);
    } else {
      expandedTables.value.add(table.id!);
      await fetchTableFields(table.id!);
    }
    
    router.push({ 
      name: 'DataSetDetail', 
      params: { id: props.datasetId },
      query: { tableId: table.id }
    });
  };

  const gotoField = (table: CBDDefTableVO, field: MedicalFieldVO) => {
    currentView.value = 'field';
    currentTableId.value = table.id;
    currentFieldId.value = field.id;
    
    router.push({ 
      name: 'DataSetDetail', 
      params: { id: props.datasetId },
      query: { tableId: table.id, fieldId: field.id }
    });
  };

  // 监听路由变化
  watch(
    () => router.currentRoute.value.query,
    (query) => {
      if (query.tableId) {
        currentTableId.value = Number(query.tableId);
        currentView.value = query.fieldId ? 'field' : 'table';
        if (query.fieldId) {
          currentFieldId.value = Number(query.fieldId);
        }
        // 确保表是展开的
        if (currentTableId.value) {
          expandedTables.value.add(currentTableId.value);
          fetchTableFields(currentTableId.value);
        }
      } else {
        currentView.value = 'overview';
        currentTableId.value = undefined;
        currentFieldId.value = undefined;
      }
    },
    { immediate: true }
  );

  onMounted(() => {
    fetchTables();
  });
</script>

<style scoped lang="scss">
  .aside {
    width: 320px;
    height: 100%;
    box-shadow: 2px 0 12px 0 rgb(0 0 0 / 10%);
    z-index: 10;
  }

  .rotate-90 {
    transform: rotate(90deg);
  }
</style>
